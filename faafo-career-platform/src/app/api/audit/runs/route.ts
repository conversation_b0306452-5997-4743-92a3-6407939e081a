import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { isAdminUser } from '@/lib/auth-utils';
import { AuditService } from '@/lib/audit';
import { z } from 'zod';
import { logger } from '@/lib/logger';

// Request validation schemas
const auditRunConfigSchema = z.object({
  enabledAnalyzers: z.array(z.string()).optional(),
  excludePatterns: z.array(z.string()).optional(),
  includePatterns: z.array(z.string()).optional(),
  severity: z.enum(['critical', 'high', 'medium', 'low']).optional(),
  categories: z.array(z.enum(['security', 'performance', 'maintainability', 'testing', 'documentation', 'architecture', 'accessibility'])).optional()
});

const auditRunsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  status: z.enum(['pending', 'running', 'completed', 'failed']).optional(),
  triggeredBy: z.string().optional()
});

// Response interfaces
interface AuditRunsResponse {
  runs: Array<{
    id: string;
    startedAt: Date;
    completedAt: Date | null;
    status: string;
    totalIssues: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    triggeredBy: string | null;
    metadata?: any;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface AuditRunCreateResponse {
  id: string;
  status: string;
  message: string;
}

// GET - List audit runs with pagination and filtering
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AuditRunsResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      
      // Check authentication - audit access requires admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      // Parse and validate query parameters
      const url = new URL(request.url);
      const queryParams = Object.fromEntries(url.searchParams.entries());
      const validation = auditRunsQuerySchema.safeParse(queryParams);

      if (!validation.success) {
        const error = new Error('Invalid query parameters') as any;
        error.statusCode = 400;
        error.details = validation.error.errors;
        throw error;
      }

      const { page, limit, status, triggeredBy } = validation.data;

      logger.info('Fetching audit runs', {
        component: 'audit_api',
        action: 'list_runs',
        userId: session.user.email,
        params: { page, limit, status, triggeredBy }
      });

      // Initialize audit service and fetch runs
      const auditService = new AuditService();
      const result = await auditService.getAuditRuns({
        page,
        limit,
        status,
        triggeredBy
      });

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    }
  );
});

// POST - Trigger new audit run
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AuditRunCreateResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 audit runs per 15 minutes
      async () => {
        const session = await getServerSession(authOptions);
        
        // Check authentication - audit operations require admin privileges
        if (!session?.user?.email || !isAdminUser(session.user.email)) {
          const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
          error.statusCode = 403;
          throw error;
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = auditRunConfigSchema.safeParse(body);

        if (!validation.success) {
          const error = new Error('Invalid audit configuration') as any;
          error.statusCode = 400;
          error.details = validation.error.errors;
          throw error;
        }

        const config = validation.data;

        logger.info('Starting new audit run', {
          component: 'audit_api',
          action: 'start_audit',
          userId: session.user.email,
          config
        });

        // Initialize audit service and start audit
        const auditService = new AuditService();
        const auditId = await auditService.startAudit(
          config,
          undefined, // No progress callback for API
          session.user.id
        );

        return NextResponse.json({
          success: true,
          data: {
            id: auditId,
            status: 'started',
            message: 'Audit run initiated successfully'
          },
          timestamp: new Date().toISOString()
        });
      }
    );
  });
});
