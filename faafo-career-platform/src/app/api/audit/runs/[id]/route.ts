import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { isAdminUser } from '@/lib/auth-utils';
import { AuditService } from '@/lib/audit';
import { logger } from '@/lib/logger';

// Response interface
interface AuditRunDetailResponse {
  id: string;
  startedAt: Date;
  completedAt: Date | null;
  status: string;
  totalIssues: number;
  criticalCount: number;
  highCount: number;
  mediumCount: number;
  lowCount: number;
  triggeredBy: string | null;
  metadata?: any;
  issues: Array<{
    id: string;
    severity: string;
    category: string;
    title: string;
    description: string;
    filePath: string;
    lineNumber: number | null;
    columnNumber: number | null;
    status: string;
    assignedToId: string | null;
    resolvedAt: Date | null;
    falsePositive: boolean;
    createdAt: Date;
    updatedAt: Date;
  }>;
  progress?: {
    currentStep: string;
    percentage: number;
    issuesFound: number;
    estimatedTimeRemaining?: number;
  };
}

// GET - Retrieve specific audit run details
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<AuditRunDetailResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      const { id } = await params;
      
      // Check authentication - audit access requires admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      if (!id) {
        const error = new Error('Audit run ID is required') as any;
        error.statusCode = 400;
        throw error;
      }

      logger.info('Fetching audit run details', {
        component: 'audit_api',
        action: 'get_run_details',
        userId: session.user.email,
        auditRunId: id
      });

      // Initialize audit service and fetch run details
      const auditService = new AuditService();
      const auditRun = await auditService.getAuditRun(id);

      if (!auditRun) {
        const error = new Error('Audit run not found') as any;
        error.statusCode = 404;
        throw error;
      }

      // Get progress information if audit is still running
      let progress;
      if (auditRun.status === 'running') {
        progress = await auditService.getAuditProgress(id);
      }

      return NextResponse.json({
        success: true,
        data: {
          ...auditRun,
          progress
        },
        timestamp: new Date().toISOString()
      });
    }
  );
});

// DELETE - Cancel or delete audit run
export const DELETE = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<{ message: string }>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      const { id } = await params;
      
      // Check authentication - audit operations require admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      if (!id) {
        const error = new Error('Audit run ID is required') as any;
        error.statusCode = 400;
        throw error;
      }

      logger.info('Cancelling/deleting audit run', {
        component: 'audit_api',
        action: 'delete_run',
        userId: session.user.email,
        auditRunId: id
      });

      // Initialize audit service and cancel/delete run
      const auditService = new AuditService();
      const result = await auditService.cancelAudit(id);

      if (!result) {
        const error = new Error('Audit run not found or cannot be cancelled') as any;
        error.statusCode = 404;
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: {
          message: 'Audit run cancelled successfully'
        },
        timestamp: new Date().toISOString()
      });
    }
  );
});
