import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { isAdminUser } from '@/lib/auth-utils';
import { AuditService } from '@/lib/audit';
import { z } from 'zod';
import { logger } from '@/lib/logger';

// Request validation schemas
const issuesQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  auditRunId: z.string().optional(),
  severity: z.enum(['critical', 'high', 'medium', 'low']).optional(),
  category: z.enum(['security', 'performance', 'maintainability', 'testing', 'documentation', 'architecture', 'accessibility']).optional(),
  status: z.enum(['open', 'assigned', 'in_progress', 'resolved', 'false_positive', 'deferred']).optional(),
  assignedToId: z.string().optional(),
  filePath: z.string().optional(),
  sortBy: z.enum(['severity', 'category', 'createdAt', 'updatedAt', 'filePath']).default('severity'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  search: z.string().optional()
});

// Response interfaces
interface AuditIssuesResponse {
  issues: Array<{
    id: string;
    auditRunId: string;
    severity: string;
    category: string;
    title: string;
    description: string;
    filePath: string;
    lineNumber: number | null;
    columnNumber: number | null;
    codeSnippet: string | null;
    recommendation: string | null;
    fixExample: string | null;
    status: string;
    assignedToId: string | null;
    resolvedAt: Date | null;
    falsePositive: boolean;
    createdAt: Date;
    updatedAt: Date;
    assignedTo?: {
      id: string;
      name: string | null;
      email: string;
    };
    auditRun: {
      id: string;
      startedAt: Date;
      status: string;
    };
    _count: {
      comments: number;
    };
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  summary: {
    totalIssues: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    openCount: number;
    resolvedCount: number;
  };
}

// GET - List audit issues with advanced filtering and pagination
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AuditIssuesResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      
      // Check authentication - audit access requires admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      // Parse and validate query parameters
      const url = new URL(request.url);
      const queryParams = Object.fromEntries(url.searchParams.entries());
      const validation = issuesQuerySchema.safeParse(queryParams);

      if (!validation.success) {
        const error = new Error('Invalid query parameters') as any;
        error.statusCode = 400;
        error.details = validation.error.errors;
        throw error;
      }

      const filters = validation.data;

      logger.info('Fetching audit issues', {
        component: 'audit_api',
        action: 'list_issues',
        userId: session.user.email,
        filters
      });

      // Initialize audit service and fetch issues
      const auditService = new AuditService();
      const result = await auditService.getIssues({
        page: filters.page,
        limit: filters.limit,
        auditRunId: filters.auditRunId,
        severity: filters.severity,
        category: filters.category,
        status: filters.status,
        assignedToId: filters.assignedToId,
        filePath: filters.filePath,
        search: filters.search
      }, {
        field: filters.sortBy,
        direction: filters.sortOrder
      });

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    }
  );
});
