import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { isAdminUser } from '@/lib/auth-utils';
import { AuditService } from '@/lib/audit';
import { z } from 'zod';
import { logger } from '@/lib/logger';

// Request validation schemas
const commentCreateSchema = z.object({
  comment: z.string()
    .min(1, 'Comment cannot be empty')
    .max(2000, 'Comment too long (max 2000 characters)')
    .trim()
});

const commentsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(20)
});

// Response interfaces
interface IssueCommentsResponse {
  comments: Array<{
    id: string;
    comment: string;
    createdAt: Date;
    updatedAt: Date;
    user: {
      id: string;
      name: string | null;
      email: string;
    };
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface CommentCreateResponse {
  id: string;
  comment: string;
  createdAt: Date;
  user: {
    id: string;
    name: string | null;
    email: string;
  };
}

// GET - List comments for an audit issue
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<IssueCommentsResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      const { id } = await params;
      
      // Check authentication - audit access requires admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      if (!id) {
        const error = new Error('Issue ID is required') as any;
        error.statusCode = 400;
        throw error;
      }

      // Parse and validate query parameters
      const url = new URL(request.url);
      const queryParams = Object.fromEntries(url.searchParams.entries());
      const validation = commentsQuerySchema.safeParse(queryParams);

      if (!validation.success) {
        const error = new Error('Invalid query parameters') as any;
        error.statusCode = 400;
        error.details = validation.error.errors;
        throw error;
      }

      const { page, limit } = validation.data;

      logger.info('Fetching issue comments', {
        component: 'audit_api',
        action: 'list_comments',
        userId: session.user.email
      });

      // Initialize audit service and fetch comments
      const auditService = new AuditService();
      const result = await auditService.getIssueComments(id, { page, limit });

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    }
  ) as Promise<NextResponse<ApiResponse<IssueCommentsResponse>>>;
});

// POST - Add comment to audit issue
export const POST = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<CommentCreateResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 comments per 15 minutes
      async () => {
        const session = await getServerSession(authOptions);
        const { id } = await params;
        
        // Check authentication - audit operations require admin privileges
        if (!session?.user?.email || !isAdminUser(session.user.email)) {
          const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
          error.statusCode = 403;
          throw error;
        }

        if (!id) {
          const error = new Error('Issue ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = commentCreateSchema.safeParse(body);

        if (!validation.success) {
          const error = new Error('Invalid comment data') as any;
          error.statusCode = 400;
          error.details = validation.error.errors;
          throw error;
        }

        const { comment } = validation.data;

        logger.info('Adding comment to audit issue', {
          component: 'audit_api',
          action: 'add_comment',
          userId: session.user.email
        });

        // Initialize audit service and add comment
        const auditService = new AuditService();
        const newComment = await auditService.addIssueComment(id, {
          comment,
          userId: session.user.id!
        });

        if (!newComment) {
          const error = new Error('Issue not found or comment creation failed') as any;
          error.statusCode = 404;
          throw error;
        }

        return NextResponse.json({
          success: true,
          data: newComment,
          timestamp: new Date().toISOString()
        });
      }
    ) as Promise<NextResponse<ApiResponse<CommentCreateResponse>>>;
  }) as Promise<NextResponse<ApiResponse<CommentCreateResponse>>>;
});
