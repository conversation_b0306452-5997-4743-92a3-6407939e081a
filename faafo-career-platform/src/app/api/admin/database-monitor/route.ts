// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { databaseMonitor } from '@/lib/database-monitor';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// GET - Get database monitoring metrics (admin only)
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 },
    async () => {
      const session = await getServerSession(authOptions);

      if (!session?.user?.id) {
        return NextResponse.json({
          success: false,
          error: 'Authentication required'
        }, { status: 401 });
      }

      // Check if user is admin (you may need to adjust this based on your user model)
      // For now, we'll allow any authenticated user in development
      if (process.env.NODE_ENV === 'production') {
        // Add admin check here when you have admin role implemented
        // const user = await prisma.user.findUnique({
        //   where: { id: session.user.id },
        //   include: { profile: true }
        // });
        // 
        // if (!user?.profile?.isAdmin) {
        //   return NextResponse.json({
        //     success: false,
        //     error: 'Admin access required'
        //   }, { status: 403 });
        // }
      }

      const { searchParams } = new URL(request.url);
      const action = searchParams.get('action');

      if (action === 'report') {
        // Generate detailed report
        const report = databaseMonitor.generateReport();
        
        return NextResponse.json({
          success: true,
          data: {
            report,
            timestamp: new Date().toISOString(),
          }
        });
      } else if (action === 'clear') {
        // Clear monitoring data
        databaseMonitor.clearMetrics();
        
        return NextResponse.json({
          success: true,
          message: 'Database monitoring metrics cleared'
        });
      } else {
        // Get current metrics
        const metrics = databaseMonitor.getQueryMetrics();
        const n1Patterns = databaseMonitor.getN1Patterns();
        
        return NextResponse.json({
          success: true,
          data: {
            metrics,
            n1Patterns,
            timestamp: new Date().toISOString(),
            monitoringActive: process.env.NODE_ENV === 'development',
          }
        });
      }
    }
  );
});

// POST - Control database monitoring (admin only)
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 },
    async () => {
      const session = await getServerSession(authOptions);

      if (!session?.user?.id) {
        return NextResponse.json({
          success: false,
          error: 'Authentication required'
        }, { status: 401 });
      }

      // Check if user is admin (you may need to adjust this based on your user model)
      if (process.env.NODE_ENV === 'production') {
        // Add admin check here when you have admin role implemented
      }

      const body = await request.json();
      const { action } = body;

      switch (action) {
        case 'start':
          // Monitoring is automatically started in development
          return NextResponse.json({
            success: true,
            message: 'Database monitoring is active in development mode',
            data: { active: process.env.NODE_ENV === 'development' }
          });

        case 'stop':
          databaseMonitor.stopMonitoring();
          return NextResponse.json({
            success: true,
            message: 'Database monitoring stopped'
          });

        case 'clear':
          databaseMonitor.clearMetrics();
          return NextResponse.json({
            success: true,
            message: 'Database monitoring metrics cleared'
          });

        case 'analyze':
          const metrics = databaseMonitor.getQueryMetrics();
          const analysis = {
            performance: {
              status: metrics.averageDuration < 50 ? 'good' : metrics.averageDuration < 100 ? 'warning' : 'critical',
              averageDuration: metrics.averageDuration,
              slowQueryCount: metrics.slowQueries.length,
            },
            n1Issues: {
              count: metrics.n1Patterns.length,
              severity: metrics.n1Patterns.length === 0 ? 'none' : 
                       metrics.n1Patterns.length < 3 ? 'low' : 
                       metrics.n1Patterns.length < 10 ? 'medium' : 'high',
              patterns: metrics.n1Patterns.map(p => ({
                query: p.baseQuery,
                count: p.count,
                totalDuration: p.totalDuration,
                avgDuration: Math.round(p.totalDuration / p.count * 100) / 100,
              })),
            },
            recommendations: generateRecommendations(metrics),
          };

          return NextResponse.json({
            success: true,
            data: analysis
          });

        default:
          return NextResponse.json({
            success: false,
            error: 'Invalid action'
          }, { status: 400 });
      }
    }
  );
});

function generateRecommendations(metrics: any): string[] {
  const recommendations: string[] = [];

  if (metrics.averageDuration > 100) {
    recommendations.push('Average query duration is high. Consider adding database indexes or optimizing queries.');
  }

  if (metrics.slowQueries.length > 10) {
    recommendations.push('Many slow queries detected. Review and optimize the slowest queries first.');
  }

  if (metrics.n1Patterns.length > 0) {
    recommendations.push(`${metrics.n1Patterns.length} N+1 query patterns detected. Use includes/select to load related data in single queries.`);
  }

  if (metrics.totalQueries > 1000) {
    recommendations.push('High query volume detected. Consider implementing query caching or connection pooling.');
  }

  if (recommendations.length === 0) {
    recommendations.push('Database performance looks good! Continue monitoring for any changes.');
  }

  return recommendations;
}
