/**
 * Audit Service
 * 
 * Main service class that provides a high-level interface for the audit system.
 * This service is used by the web dashboard and API endpoints.
 */

import { CoreAuditEngine } from './core-audit-engine';
import { AuditStorage } from './storage/audit-storage';
import {
  AuditRunConfig,
  AuditRunResult,
  AuditRunSummary,
  BaseIssue,
  IssueFilter,
  IssueSortOptions,
  PaginatedIssues,
  AuditAPIResponse,
  AuditProgress,
  AuditProgressCallback,
  IssueStatus,
  AnalysisContext
} from './types';
import { logger } from '../logger';

export class AuditService {
  private storage: AuditStorage;
  private activeAudits: Map<string, CoreAuditEngine> = new Map();

  constructor() {
    this.storage = new AuditStorage();
  }

  /**
   * Start a new audit run
   */
  async startAudit(
    config: AuditRunConfig = {},
    progressCallback?: AuditProgressCallback,
    triggeredBy?: string
  ): Promise<string> {
    try {
      logger.info('Starting new audit run');

      // Create analysis context
      const context: Partial<AnalysisContext> = {
        projectRoot: process.cwd(),
        excludePatterns: [
          'node_modules/**',
          '.next/**',
          'dist/**',
          'build/**',
          'coverage/**',
          '**/*.test.ts',
          '**/*.test.tsx',
          '**/*.spec.ts',
          '**/*.spec.tsx'
        ]
      };

      // Initialize audit engine
      const engine = new CoreAuditEngine(context, progressCallback);
      
      // Add metadata
      const auditConfig = {
        ...config
      };

      // Start audit (non-blocking)
      const auditPromise = engine.runAudit(auditConfig);
      
      // Get audit ID from the promise (this is a simplified approach)
      const result = await auditPromise;
      
      logger.info(`Audit started with ID: ${result.id}`);
      return result.id;

    } catch (error) {
      logger.error('Failed to start audit:', error);
      throw error;
    }
  }

  /**
   * Get audit run by ID
   */
  async getAuditRun(auditId: string): Promise<AuditRunResult | null> {
    try {
      return await this.storage.getAuditRun(auditId);
    } catch (error) {
      logger.error(`Failed to get audit run ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Get audit run summaries with pagination
   */
  async getAuditRuns(page: number = 1, limit: number = 20): Promise<AuditAPIResponse<AuditRunSummary[]>> {
    try {
      return await this.storage.getAuditRuns(page, limit);
    } catch (error) {
      logger.error('Failed to get audit runs:', error);
      return {
        success: false,
        error: 'Failed to retrieve audit runs'
      };
    }
  }

  /**
   * Get issues with filtering and pagination
   */
  async getIssues(
    filter: IssueFilter = {},
    sort: IssueSortOptions = { field: 'severity', direction: 'desc' },
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedIssues> {
    try {
      return await this.storage.getIssues(filter, sort, page, limit);
    } catch (error) {
      logger.error('Failed to get issues:', error);
      throw error;
    }
  }

  /**
   * Update issue status
   */
  async updateIssueStatus(
    issueId: string,
    status: IssueStatus,
    assignedToId?: string,
    resolvedAt?: Date
  ): Promise<void> {
    try {
      await this.storage.updateIssueStatus(issueId, status, assignedToId, resolvedAt);
      logger.info(`Updated issue ${issueId} status to ${status}`);
    } catch (error) {
      logger.error(`Failed to update issue ${issueId}:`, error);
      throw error;
    }
  }

  /**
   * Add comment to issue
   */
  async addIssueComment(issueId: string, userId: string, comment: string): Promise<void> {
    try {
      await this.storage.addIssueComment(issueId, userId, comment);
      logger.info(`Added comment to issue ${issueId}`);
    } catch (error) {
      logger.error(`Failed to add comment to issue ${issueId}:`, error);
      throw error;
    }
  }

  /**
   * Get issue comments
   */
  async getIssueComments(issueId: string): Promise<any[]> {
    try {
      return await this.storage.getIssueComments(issueId);
    } catch (error) {
      logger.error(`Failed to get comments for issue ${issueId}:`, error);
      throw error;
    }
  }

  /**
   * Get audit statistics
   */
  async getAuditStatistics(days: number = 30): Promise<any> {
    try {
      return await this.storage.getAuditStatistics(days);
    } catch (error) {
      logger.error('Failed to get audit statistics:', error);
      throw error;
    }
  }

  /**
   * Cancel running audit
   */
  async cancelAudit(auditId: string): Promise<void> {
    try {
      const engine = this.activeAudits.get(auditId);
      if (engine) {
        // In a real implementation, you'd need to add cancellation support to the engine
        this.activeAudits.delete(auditId);
        logger.info(`Cancelled audit ${auditId}`);
      }
    } catch (error) {
      logger.error(`Failed to cancel audit ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Clean up old audit runs
   */
  async cleanupOldAudits(daysToKeep: number = 90): Promise<number> {
    try {
      const deletedCount = await this.storage.cleanupOldAudits(daysToKeep);
      logger.info(`Cleaned up ${deletedCount} old audit runs`);
      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup old audits:', error);
      throw error;
    }
  }

  /**
   * Get issue trends over time
   */
  async getIssueTrends(days: number = 30): Promise<any> {
    try {
      // This would be implemented to show trends in issue counts over time
      // For now, return basic statistics
      return await this.getAuditStatistics(days);
    } catch (error) {
      logger.error('Failed to get issue trends:', error);
      throw error;
    }
  }

  /**
   * Export audit results
   */
  async exportAuditResults(auditId: string, format: 'json' | 'csv' | 'pdf' = 'json'): Promise<Buffer | string> {
    try {
      const auditRun = await this.getAuditRun(auditId);
      if (!auditRun) {
        throw new Error(`Audit run ${auditId} not found`);
      }

      switch (format) {
        case 'json':
          return JSON.stringify(auditRun, null, 2);
        
        case 'csv':
          return this.convertToCSV(auditRun.issues);
        
        case 'pdf':
          // Would implement PDF generation
          throw new Error('PDF export not implemented yet');
        
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      logger.error(`Failed to export audit results for ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Convert issues to CSV format
   */
  private convertToCSV(issues: BaseIssue[]): string {
    const headers = [
      'Severity',
      'Category',
      'Title',
      'Description',
      'File Path',
      'Line Number',
      'Recommendation'
    ];

    const rows = issues.map(issue => [
      issue.severity,
      issue.category,
      `"${issue.title.replace(/"/g, '""')}"`,
      `"${issue.description.replace(/"/g, '""')}"`,
      issue.filePath,
      issue.lineNumber || '',
      `"${(issue.recommendation || '').replace(/"/g, '""')}"`
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  /**
   * Get audit health score
   */
  async getHealthScore(auditId?: string): Promise<number> {
    try {
      let issues: BaseIssue[];

      if (auditId) {
        const auditRun = await this.getAuditRun(auditId);
        if (!auditRun) {
          throw new Error(`Audit run ${auditId} not found`);
        }
        issues = auditRun.issues;
      } else {
        // Get issues from the most recent audit
        const recentAudits = await this.getAuditRuns(1, 1);
        if (!recentAudits.success || !recentAudits.data || recentAudits.data.length === 0) {
          return 100; // No audits = perfect score
        }
        
        const latestAudit = await this.getAuditRun(recentAudits.data[0].id);
        if (!latestAudit) {
          return 100;
        }
        issues = latestAudit.issues;
      }

      // Calculate health score based on issue severity
      const weights = {
        CRITICAL: 10,
        HIGH: 5,
        MEDIUM: 2,
        LOW: 1
      };

      const totalWeight = issues.reduce((sum, issue) => {
        return sum + (weights[issue.severity as keyof typeof weights] || 0);
      }, 0);

      // Convert to 0-100 scale (lower weight = higher score)
      const maxPossibleWeight = 1000; // Arbitrary max for scaling
      const score = Math.max(0, Math.min(100, 100 - (totalWeight / maxPossibleWeight) * 100));

      return Math.round(score);
    } catch (error) {
      logger.error('Failed to calculate health score:', error);
      return 0;
    }
  }

  /**
   * Get recommendations based on audit results
   */
  async getRecommendations(auditId: string): Promise<string[]> {
    try {
      const auditRun = await this.getAuditRun(auditId);
      if (!auditRun) {
        throw new Error(`Audit run ${auditId} not found`);
      }

      const recommendations: string[] = [];

      // Analyze issue patterns and generate recommendations
      const criticalIssues = auditRun.issues.filter(i => i.severity === 'CRITICAL');
      const securityIssues = auditRun.issues.filter(i => i.category === 'SECURITY');
      const performanceIssues = auditRun.issues.filter(i => i.category === 'PERFORMANCE');

      if (criticalIssues.length > 0) {
        recommendations.push(`Address ${criticalIssues.length} critical issues immediately`);
      }

      if (securityIssues.length > 0) {
        recommendations.push(`Review ${securityIssues.length} security vulnerabilities`);
      }

      if (performanceIssues.length > 0) {
        recommendations.push(`Optimize ${performanceIssues.length} performance issues`);
      }

      if (auditRun.totalIssues > 100) {
        recommendations.push('Consider implementing automated code quality gates');
      }

      if (recommendations.length === 0) {
        recommendations.push('Great job! No major issues found');
      }

      return recommendations;
    } catch (error) {
      logger.error(`Failed to get recommendations for ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Close database connections
   */
  async disconnect(): Promise<void> {
    await this.storage.disconnect();
  }
}
