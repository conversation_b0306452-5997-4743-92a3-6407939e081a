/**
 * TypeScript Analyzer
 * 
 * Analyzes TypeScript code for type issues, compilation errors,
 * and code quality problems using the TypeScript compiler API.
 */

import * as ts from 'typescript';
import * as path from 'path';
import { promises as fs } from 'fs';
import {
  TypeScriptIssue,
  AnalysisContext,
  IssueSeverity,
  IssueCategory
} from '../types';
import { logger } from '../../logger';

export class TypeScriptAnalyzer {
  private context: AnalysisContext;
  private program: ts.Program | null = null;

  constructor(context: AnalysisContext) {
    this.context = context;
  }

  /**
   * Initialize TypeScript program
   */
  private async initializeProgram(): Promise<void> {
    try {
      const tsconfigPath = this.context.configFiles.tsconfig;
      
      if (!tsconfigPath || !await this.fileExists(tsconfigPath)) {
        logger.warn('No tsconfig.json found, using default TypeScript configuration');
        return;
      }

      // Read and parse tsconfig.json
      const configFile = ts.readConfigFile(tsconfigPath, ts.sys.readFile);
      if (configFile.error) {
        logger.error('Error reading tsconfig.json:', new Error(String(configFile.error)));
        return;
      }

      const parsedConfig = ts.parseJsonConfigFileContent(
        configFile.config,
        ts.sys,
        path.dirname(tsconfigPath)
      );

      if (parsedConfig.errors.length > 0) {
        logger.error('Error parsing tsconfig.json:', new Error(parsedConfig.errors.map(e => String(e)).join(', ')));
        return;
      }

      // Create TypeScript program
      this.program = ts.createProgram(parsedConfig.fileNames, parsedConfig.options);
      
      logger.info('TypeScript program initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize TypeScript program:', error);
    }
  }

  /**
   * Analyze TypeScript code
   */
  async analyze(): Promise<TypeScriptIssue[]> {
    try {
      await this.initializeProgram();

      if (!this.program) {
        logger.warn('TypeScript program not initialized, skipping analysis');
        return [];
      }

      const issues: TypeScriptIssue[] = [];

      // Get compilation diagnostics
      const diagnostics = [
        ...this.program.getConfigFileParsingDiagnostics(),
        ...this.program.getOptionsDiagnostics(),
        ...this.program.getGlobalDiagnostics(),
        ...this.program.getSyntacticDiagnostics(),
        ...this.program.getSemanticDiagnostics()
      ];

      logger.info(`Found ${diagnostics.length} TypeScript diagnostics`);

      // Convert diagnostics to issues
      for (const diagnostic of diagnostics) {
        const issue = await this.convertDiagnosticToIssue(diagnostic);
        if (issue) {
          issues.push(issue);
        }
      }

      // Perform additional custom analysis
      const customIssues = await this.performCustomAnalysis();
      issues.push(...customIssues);

      logger.info(`TypeScript analysis completed: ${issues.length} issues found`);
      return issues;

    } catch (error) {
      logger.error('TypeScript analysis failed:', error);
      return [];
    }
  }

  /**
   * Convert TypeScript diagnostic to audit issue
   */
  private async convertDiagnosticToIssue(diagnostic: ts.Diagnostic): Promise<TypeScriptIssue | null> {
    try {
      const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
      
      let filePath = 'unknown';
      let lineNumber: number | undefined;
      let columnNumber: number | undefined;
      let codeSnippet: string | undefined;

      if (diagnostic.file && diagnostic.start !== undefined) {
        filePath = path.relative(this.context.projectRoot, diagnostic.file.fileName);
        const position = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
        lineNumber = position.line + 1;
        columnNumber = position.character + 1;
        codeSnippet = await this.getCodeSnippet(diagnostic.file.fileName, lineNumber);
      }

      // Skip issues in excluded files
      if (this.shouldExcludeFile(filePath)) {
        return null;
      }

      const severity = this.mapDiagnosticSeverityToIssueSeverity(diagnostic.category);
      const category = this.determineDiagnosticCategory(diagnostic.code);

      const issue: TypeScriptIssue = {
        severity,
        category,
        title: `TypeScript: ${this.getDiagnosticTitle(diagnostic.code)}`,
        description: message,
        filePath,
        lineNumber,
        columnNumber,
        codeSnippet,
        recommendation: this.generateRecommendation(diagnostic),
        fixExample: this.generateFixExample(diagnostic),
        tsErrorCode: diagnostic.code,
        tsErrorMessage: message,
        metadata: {
          diagnosticCategory: ts.DiagnosticCategory[diagnostic.category],
          source: diagnostic.source,
          relatedInformation: diagnostic.relatedInformation?.map(info => ({
            file: info.file?.fileName,
            message: ts.flattenDiagnosticMessageText(info.messageText, '\n')
          }))
        }
      };

      return issue;
    } catch (error) {
      logger.error('Failed to convert diagnostic to issue:', error);
      return null;
    }
  }

  /**
   * Map TypeScript diagnostic category to issue severity
   */
  private mapDiagnosticSeverityToIssueSeverity(category: ts.DiagnosticCategory): IssueSeverity {
    switch (category) {
      case ts.DiagnosticCategory.Error:
        return IssueSeverity.HIGH;
      case ts.DiagnosticCategory.Warning:
        return IssueSeverity.MEDIUM;
      case ts.DiagnosticCategory.Suggestion:
        return IssueSeverity.LOW;
      case ts.DiagnosticCategory.Message:
        return IssueSeverity.LOW;
      default:
        return IssueSeverity.MEDIUM;
    }
  }

  /**
   * Determine issue category based on diagnostic code
   */
  private determineDiagnosticCategory(code: number): IssueCategory.MAINTAINABILITY | IssueCategory.ARCHITECTURE {
    // Common TypeScript error codes and their categories
    const categoryMap: Record<number, IssueCategory.MAINTAINABILITY | IssueCategory.ARCHITECTURE> = {
      // Type errors
      2322: IssueCategory.MAINTAINABILITY, // Type 'X' is not assignable to type 'Y'
      2339: IssueCategory.MAINTAINABILITY, // Property 'X' does not exist on type 'Y'
      2345: IssueCategory.MAINTAINABILITY, // Argument of type 'X' is not assignable to parameter of type 'Y'
      
      // Import/export errors
      2307: IssueCategory.ARCHITECTURE, // Cannot find module
      2305: IssueCategory.ARCHITECTURE, // Module has no exported member
      
      // Unused code
      6133: IssueCategory.MAINTAINABILITY, // Declared but never used
      6196: IssueCategory.MAINTAINABILITY, // Declared but never read
      
      // Strict mode issues
      2531: IssueCategory.MAINTAINABILITY, // Object is possibly 'null'
      2532: IssueCategory.MAINTAINABILITY, // Object is possibly 'undefined'
      2571: IssueCategory.MAINTAINABILITY, // Object is of type 'unknown'
    };

    return categoryMap[code] || IssueCategory.MAINTAINABILITY;
  }

  /**
   * Get diagnostic title based on error code
   */
  private getDiagnosticTitle(code: number): string {
    const titleMap: Record<number, string> = {
      2322: 'Type Assignment Error',
      2339: 'Property Not Found',
      2345: 'Argument Type Mismatch',
      2307: 'Module Not Found',
      2305: 'Export Not Found',
      6133: 'Unused Declaration',
      6196: 'Unused Variable',
      2531: 'Possible Null Reference',
      2532: 'Possible Undefined Reference',
      2571: 'Unknown Type Usage'
    };

    return titleMap[code] || `TS${code}`;
  }

  /**
   * Generate recommendation for diagnostic
   */
  private generateRecommendation(diagnostic: ts.Diagnostic): string {
    const code = diagnostic.code;
    
    const recommendations: Record<number, string> = {
      2322: 'Check the type definitions and ensure the assigned value matches the expected type',
      2339: 'Verify the property name exists on the type or add it to the interface/type definition',
      2345: 'Check the function signature and provide arguments of the correct type',
      2307: 'Install the missing module or check the import path',
      2305: 'Check the export name or add the missing export to the module',
      6133: 'Remove the unused declaration or prefix with underscore if intentionally unused',
      6196: 'Remove the unused variable or use it in the code',
      2531: 'Add null check or use optional chaining (?.) operator',
      2532: 'Add undefined check or use optional chaining (?.) operator',
      2571: 'Add proper type annotations or type guards'
    };

    return recommendations[code] || 'Review the TypeScript error and fix according to the compiler message';
  }

  /**
   * Generate fix example for diagnostic
   */
  private generateFixExample(diagnostic: ts.Diagnostic): string | undefined {
    const code = diagnostic.code;
    
    const examples: Record<number, string> = {
      2531: 'if (obj !== null) { /* use obj */ } or obj?.property',
      2532: 'if (obj !== undefined) { /* use obj */ } or obj?.property',
      2571: 'if (typeof obj === "string") { /* use obj as string */ }',
      6133: 'Remove the declaration or rename to _variableName',
      6196: 'Remove the variable or use it: console.log(variableName)'
    };

    return examples[code];
  }

  /**
   * Perform custom analysis beyond TypeScript diagnostics
   */
  private async performCustomAnalysis(): Promise<TypeScriptIssue[]> {
    const issues: TypeScriptIssue[] = [];

    if (!this.program) {
      return issues;
    }

    try {
      // Analyze for common patterns and anti-patterns
      const sourceFiles = this.program.getSourceFiles();
      
      for (const sourceFile of sourceFiles) {
        // Skip declaration files and node_modules
        if (sourceFile.isDeclarationFile || sourceFile.fileName.includes('node_modules')) {
          continue;
        }

        const filePath = path.relative(this.context.projectRoot, sourceFile.fileName);
        
        if (this.shouldExcludeFile(filePath)) {
          continue;
        }

        // Check for any types
        const anyTypeIssues = this.findAnyTypeUsage(sourceFile);
        issues.push(...anyTypeIssues);

        // Check for complex functions
        const complexityIssues = this.findComplexFunctions(sourceFile);
        issues.push(...complexityIssues);

        // Check for missing return types
        const returnTypeIssues = this.findMissingReturnTypes(sourceFile);
        issues.push(...returnTypeIssues);
      }

      logger.info(`Custom TypeScript analysis found ${issues.length} additional issues`);
    } catch (error) {
      logger.error('Custom TypeScript analysis failed:', error);
    }

    return issues;
  }

  /**
   * Find usage of 'any' type
   */
  private findAnyTypeUsage(sourceFile: ts.SourceFile): TypeScriptIssue[] {
    const issues: TypeScriptIssue[] = [];
    const filePath = path.relative(this.context.projectRoot, sourceFile.fileName);

    const visit = (node: ts.Node) => {
      if (ts.isTypeReferenceNode(node) && node.typeName.getText() === 'any') {
        const position = sourceFile.getLineAndCharacterOfPosition(node.getStart());
        
        issues.push({
          severity: IssueSeverity.MEDIUM,
          category: IssueCategory.MAINTAINABILITY,
          title: 'TypeScript: Any Type Usage',
          description: 'Usage of "any" type reduces type safety',
          filePath,
          lineNumber: position.line + 1,
          columnNumber: position.character + 1,
          recommendation: 'Replace "any" with specific types for better type safety',
          fixExample: 'Define proper interfaces or use union types instead of any',
          tsErrorCode: 0,
          tsErrorMessage: 'Any type usage detected',
          metadata: {
            nodeKind: ts.SyntaxKind[node.kind],
            customAnalysis: 'any-type-usage'
          }
        });
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return issues;
  }

  /**
   * Find functions with high complexity
   */
  private findComplexFunctions(sourceFile: ts.SourceFile): TypeScriptIssue[] {
    const issues: TypeScriptIssue[] = [];
    const filePath = path.relative(this.context.projectRoot, sourceFile.fileName);

    const visit = (node: ts.Node) => {
      if (ts.isFunctionDeclaration(node) || ts.isMethodDeclaration(node) || ts.isArrowFunction(node)) {
        const complexity = this.calculateCyclomaticComplexity(node);
        
        if (complexity > 10) {
          const position = sourceFile.getLineAndCharacterOfPosition(node.getStart());
          const functionName = this.getFunctionName(node);
          
          issues.push({
            severity: complexity > 20 ? IssueSeverity.HIGH : IssueSeverity.MEDIUM,
            category: IssueCategory.MAINTAINABILITY,
            title: 'TypeScript: High Function Complexity',
            description: `Function "${functionName}" has cyclomatic complexity of ${complexity}`,
            filePath,
            lineNumber: position.line + 1,
            columnNumber: position.character + 1,
            recommendation: 'Break down complex functions into smaller, more manageable functions',
            fixExample: 'Extract logic into separate functions or use early returns to reduce nesting',
            tsErrorCode: 0,
            tsErrorMessage: `High complexity: ${complexity}`,
            metadata: {
              complexity,
              functionName,
              customAnalysis: 'function-complexity'
            }
          });
        }
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return issues;
  }

  /**
   * Find functions missing return type annotations
   */
  private findMissingReturnTypes(sourceFile: ts.SourceFile): TypeScriptIssue[] {
    const issues: TypeScriptIssue[] = [];
    const filePath = path.relative(this.context.projectRoot, sourceFile.fileName);

    const visit = (node: ts.Node) => {
      if ((ts.isFunctionDeclaration(node) || ts.isMethodDeclaration(node)) && !node.type) {
        const position = sourceFile.getLineAndCharacterOfPosition(node.getStart());
        const functionName = this.getFunctionName(node);
        
        issues.push({
          severity: IssueSeverity.LOW,
          category: IssueCategory.MAINTAINABILITY,
          title: 'TypeScript: Missing Return Type',
          description: `Function "${functionName}" is missing explicit return type annotation`,
          filePath,
          lineNumber: position.line + 1,
          columnNumber: position.character + 1,
          recommendation: 'Add explicit return type annotations to improve code clarity',
          fixExample: `function ${functionName}(): ReturnType { ... }`,
          tsErrorCode: 0,
          tsErrorMessage: 'Missing return type annotation',
          metadata: {
            functionName,
            customAnalysis: 'missing-return-type'
          }
        });
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return issues;
  }

  /**
   * Calculate cyclomatic complexity of a function
   */
  private calculateCyclomaticComplexity(node: ts.Node): number {
    let complexity = 1; // Base complexity

    const visit = (child: ts.Node) => {
      // Increment complexity for decision points
      if (ts.isIfStatement(child) ||
          ts.isWhileStatement(child) ||
          ts.isForStatement(child) ||
          ts.isForInStatement(child) ||
          ts.isForOfStatement(child) ||
          ts.isSwitchStatement(child) ||
          ts.isConditionalExpression(child) ||
          ts.isCatchClause(child)) {
        complexity++;
      }

      // Handle logical operators
      if (ts.isBinaryExpression(child) && 
          (child.operatorToken.kind === ts.SyntaxKind.AmpersandAmpersandToken ||
           child.operatorToken.kind === ts.SyntaxKind.BarBarToken)) {
        complexity++;
      }

      ts.forEachChild(child, visit);
    };

    ts.forEachChild(node, visit);
    return complexity;
  }

  /**
   * Get function name from node
   */
  private getFunctionName(node: ts.FunctionDeclaration | ts.MethodDeclaration | ts.ArrowFunction): string {
    if (ts.isFunctionDeclaration(node) && node.name) {
      return node.name.text;
    }
    if (ts.isMethodDeclaration(node) && node.name) {
      return node.name.getText();
    }
    return 'anonymous';
  }

  /**
   * Get code snippet around the issue line
   */
  private async getCodeSnippet(filePath: string, lineNumber: number, contextLines: number = 2): Promise<string | undefined> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      
      const startLine = Math.max(0, lineNumber - contextLines - 1);
      const endLine = Math.min(lines.length, lineNumber + contextLines);
      
      const snippet = lines.slice(startLine, endLine)
        .map((line, index) => {
          const actualLineNumber = startLine + index + 1;
          const marker = actualLineNumber === lineNumber ? '> ' : '  ';
          return `${marker}${actualLineNumber.toString().padStart(3)}: ${line}`;
        })
        .join('\n');
      
      return snippet;
    } catch (error) {
      logger.error(`Failed to get code snippet for ${filePath}:${lineNumber}:`, error);
      return undefined;
    }
  }

  /**
   * Check if file should be excluded
   */
  private shouldExcludeFile(relativePath: string): boolean {
    return this.context.excludePatterns.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
      return regex.test(relativePath);
    });
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
